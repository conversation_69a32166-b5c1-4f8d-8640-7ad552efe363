import 'dart:async';
import 'dart:collection';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';

/// Advanced performance monitoring system for production optimization
class AdvancedPerformanceMonitor {
  static AdvancedPerformanceMonitor? _instance;
  static bool _isInitialized = false;

  // Performance metrics
  static final Queue<double> _fpsHistory = Queue<double>();
  static final Queue<int> _frameTimeHistory = Queue<int>();
  static final Map<String, PerformanceMetric> _metrics = {};
  static final Map<String, Stopwatch> _activeOperations = {};

  // Monitoring settings
  static const int _maxHistorySize = 100;
  static const Duration _reportingInterval = Duration(minutes: 1);
  static Timer? _reportingTimer;

  // Performance thresholds (reduced sensitivity for less log spam)
  static const double _targetFps = 60.0;
  static const double _warningFpsThreshold = 30.0; // Only warn below 30 FPS
  static const double _criticalFpsThreshold = 20.0; // Only warn below 20 FPS
  static const int _maxFrameTimeMs = 16; // 60 FPS = 16.67ms per frame

  factory AdvancedPerformanceMonitor() {
    _instance ??= AdvancedPerformanceMonitor._internal();
    return _instance!;
  }

  AdvancedPerformanceMonitor._internal();

  /// Initialize performance monitoring
  static void initialize() {
    if (_isInitialized) return;

    debugPrint('🚀 Initializing Advanced Performance Monitor...');

    _startFpsMonitoring();
    _startFrameTimeMonitoring();
    _startPeriodicReporting();

    _isInitialized = true;
    debugPrint('✅ Performance monitoring initialized');
  }

  /// Start FPS monitoring
  static void _startFpsMonitoring() {
    int frameCount = 0;
    Duration totalElapsed = Duration.zero;
    Duration lastTimestamp = Duration.zero;

    SchedulerBinding.instance.addPersistentFrameCallback((timeStamp) {
      if (lastTimestamp == Duration.zero) {
        lastTimestamp = timeStamp;
        return;
      }

      frameCount++;
      totalElapsed += timeStamp - lastTimestamp;
      lastTimestamp = timeStamp;

      // Calculate FPS every 60 frames
      if (frameCount >= 60) {
        final fps = (frameCount * 1000000) / totalElapsed.inMicroseconds;
        _recordFps(fps);
        frameCount = 0;
        totalElapsed = Duration.zero;
      }
    });
  }

  /// Start frame time monitoring
  static void _startFrameTimeMonitoring() {
    Duration? lastFrameTime;

    SchedulerBinding.instance.addPersistentFrameCallback((timeStamp) {
      if (lastFrameTime != null) {
        final frameTime = (timeStamp - lastFrameTime!).inMilliseconds;
        _recordFrameTime(frameTime);
      }
      lastFrameTime = timeStamp;
    });
  }

  /// Record FPS measurement
  static void _recordFps(double fps) {
    _fpsHistory.add(fps);
    if (_fpsHistory.length > _maxHistorySize) {
      _fpsHistory.removeFirst();
    }

    // Check for performance issues
    if (fps < _criticalFpsThreshold) {
      _reportPerformanceIssue('Critical FPS drop: ${fps.toStringAsFixed(1)}');
    } else if (fps < _warningFpsThreshold) {
      _reportPerformanceIssue('Low FPS warning: ${fps.toStringAsFixed(1)}');
    }
  }

  /// Record frame time measurement
  static void _recordFrameTime(int frameTimeMs) {
    _frameTimeHistory.add(frameTimeMs);
    if (_frameTimeHistory.length > _maxHistorySize) {
      _frameTimeHistory.removeFirst();
    }

    // Check for significant frame drops (reduced verbosity)
    if (frameTimeMs > _maxFrameTimeMs * 4) {
      // Only report drops over 66ms
      _reportPerformanceIssue('Frame drop detected: ${frameTimeMs}ms');
    }
  }

  /// Start periodic performance reporting
  static void _startPeriodicReporting() {
    _reportingTimer = Timer.periodic(_reportingInterval, (_) {
      _generatePerformanceReport();
    });
  }

  /// Report performance issue
  static void _reportPerformanceIssue(String issue) {
    if (kDebugMode) {
      debugPrint('⚠️ Performance Issue: $issue');
    }

    // In production, you could send this to analytics
    _recordMetric('performance_issues', 1);
  }

  /// Start measuring an operation
  static void startOperation(String operationName) {
    _activeOperations[operationName] = Stopwatch()..start();
  }

  /// End measuring an operation
  static void endOperation(String operationName) {
    final stopwatch = _activeOperations.remove(operationName);
    if (stopwatch != null) {
      stopwatch.stop();
      final duration = stopwatch.elapsedMilliseconds;

      _recordMetric('operation_$operationName', duration);

      if (kDebugMode) {
        debugPrint('⏱️ $operationName: ${duration}ms');
      }
    }
  }

  /// Measure operation with automatic timing
  static Future<T> measureOperation<T>(
    String operationName,
    Future<T> Function() operation,
  ) async {
    startOperation(operationName);
    try {
      final result = await operation();
      endOperation(operationName);
      return result;
    } catch (e) {
      endOperation(operationName);
      _recordMetric('operation_${operationName}_errors', 1);
      rethrow;
    }
  }

  /// Record custom metric
  static void _recordMetric(String name, num value) {
    if (!_metrics.containsKey(name)) {
      _metrics[name] = PerformanceMetric(name);
    }
    _metrics[name]!.addValue(value);
  }

  /// Record custom metric (public)
  static void recordMetric(String name, num value) {
    _recordMetric(name, value);
  }

  /// Generate comprehensive performance report
  static void _generatePerformanceReport() {
    if (!kDebugMode) return;

    final report = StringBuffer();
    report.writeln('📊 Performance Report:');

    // FPS statistics
    if (_fpsHistory.isNotEmpty) {
      final avgFps = _fpsHistory.reduce((a, b) => a + b) / _fpsHistory.length;
      final minFps = _fpsHistory.reduce((a, b) => a < b ? a : b);
      final maxFps = _fpsHistory.reduce((a, b) => a > b ? a : b);

      report.writeln(
          '  FPS: avg=${avgFps.toStringAsFixed(1)}, min=${minFps.toStringAsFixed(1)}, max=${maxFps.toStringAsFixed(1)}');
    }

    // Frame time statistics
    if (_frameTimeHistory.isNotEmpty) {
      final avgFrameTime =
          _frameTimeHistory.reduce((a, b) => a + b) / _frameTimeHistory.length;
      final maxFrameTime = _frameTimeHistory.reduce((a, b) => a > b ? a : b);

      report.writeln(
          '  Frame Time: avg=${avgFrameTime.toStringAsFixed(1)}ms, max=${maxFrameTime}ms');
    }

    // Custom metrics
    _metrics.forEach((name, metric) {
      report.writeln('  $name: ${metric.getSummary()}');
    });

    debugPrint(report.toString());
  }

  /// Get current performance statistics
  static Map<String, dynamic> getPerformanceStats() {
    final stats = <String, dynamic>{};

    // FPS stats
    if (_fpsHistory.isNotEmpty) {
      final avgFps = _fpsHistory.reduce((a, b) => a + b) / _fpsHistory.length;
      final minFps = _fpsHistory.reduce((a, b) => a < b ? a : b);
      final maxFps = _fpsHistory.reduce((a, b) => a > b ? a : b);

      stats['fps'] = {
        'average': avgFps,
        'minimum': minFps,
        'maximum': maxFps,
        'current': _fpsHistory.isNotEmpty ? _fpsHistory.last : 0,
      };
    }

    // Frame time stats
    if (_frameTimeHistory.isNotEmpty) {
      final avgFrameTime =
          _frameTimeHistory.reduce((a, b) => a + b) / _frameTimeHistory.length;
      final maxFrameTime = _frameTimeHistory.reduce((a, b) => a > b ? a : b);

      stats['frameTime'] = {
        'average': avgFrameTime,
        'maximum': maxFrameTime,
      };
    }

    // Custom metrics
    final customMetrics = <String, dynamic>{};
    _metrics.forEach((name, metric) {
      customMetrics[name] = metric.toMap();
    });
    stats['customMetrics'] = customMetrics;

    return stats;
  }

  /// Check if performance is healthy
  static bool isPerformanceHealthy() {
    if (_fpsHistory.isEmpty) return true;

    final recentFps = _fpsHistory.length >= 10
        ? _fpsHistory.skip(_fpsHistory.length - 10)
        : _fpsHistory;

    final avgRecentFps = recentFps.reduce((a, b) => a + b) / recentFps.length;

    return avgRecentFps >= _warningFpsThreshold;
  }

  /// Get performance grade
  static String getPerformanceGrade() {
    if (_fpsHistory.isEmpty) return 'Unknown';

    final avgFps = _fpsHistory.reduce((a, b) => a + b) / _fpsHistory.length;

    if (avgFps >= 55) return 'Excellent';
    if (avgFps >= 45) return 'Good';
    if (avgFps >= 30) return 'Fair';
    return 'Poor';
  }

  /// Dispose performance monitor
  static void dispose() {
    _reportingTimer?.cancel();
    _fpsHistory.clear();
    _frameTimeHistory.clear();
    _metrics.clear();
    _activeOperations.clear();
    _isInitialized = false;
  }
}

/// Performance metric class
class PerformanceMetric {
  final String name;
  final List<num> _values = [];
  DateTime _lastUpdated = DateTime.now();

  PerformanceMetric(this.name);

  void addValue(num value) {
    _values.add(value);
    _lastUpdated = DateTime.now();

    // Keep only recent values
    if (_values.length > 100) {
      _values.removeAt(0);
    }
  }

  double get average =>
      _values.isEmpty ? 0 : _values.reduce((a, b) => a + b) / _values.length;
  num get minimum =>
      _values.isEmpty ? 0 : _values.reduce((a, b) => a < b ? a : b);
  num get maximum =>
      _values.isEmpty ? 0 : _values.reduce((a, b) => a > b ? a : b);
  num get latest => _values.isEmpty ? 0 : _values.last;
  int get count => _values.length;

  String getSummary() {
    return 'avg=${average.toStringAsFixed(1)}, min=$minimum, max=$maximum, count=$count';
  }

  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'average': average,
      'minimum': minimum,
      'maximum': maximum,
      'latest': latest,
      'count': count,
      'lastUpdated': _lastUpdated.toIso8601String(),
    };
  }
}

/// Performance overlay widget for debugging
class PerformanceOverlay extends StatefulWidget {
  final Widget child;
  final bool enabled;

  const PerformanceOverlay({
    Key? key,
    required this.child,
    this.enabled = kDebugMode,
  }) : super(key: key);

  @override
  State<PerformanceOverlay> createState() => _PerformanceOverlayState();
}

class _PerformanceOverlayState extends State<PerformanceOverlay> {
  Timer? _updateTimer;
  String _performanceText = '';

  @override
  void initState() {
    super.initState();
    if (widget.enabled) {
      _startUpdating();
    }
  }

  void _startUpdating() {
    _updateTimer = Timer.periodic(const Duration(seconds: 1), (_) {
      if (mounted) {
        setState(() {
          final stats = AdvancedPerformanceMonitor.getPerformanceStats();
          final fps = stats['fps']?['current'] ?? 0;
          final grade = AdvancedPerformanceMonitor.getPerformanceGrade();
          _performanceText = '${fps.toStringAsFixed(1)} FPS\n$grade';
        });
      }
    });
  }

  @override
  void dispose() {
    _updateTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.enabled) {
      return widget.child;
    }

    return Stack(
      children: [
        widget.child,
        Positioned(
          top: 50,
          right: 10,
          child: IgnorePointer(
            child: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.7),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                _performanceText,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
        ),
      ],
    );
  }
}
