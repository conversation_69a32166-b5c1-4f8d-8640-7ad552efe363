import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../utils/advanced_performance_monitor.dart';
import '../utils/memory_manager.dart';

/// Quick performance debug panel for easy monitoring
class PerformanceDebugPanel extends StatefulWidget {
  const PerformanceDebugPanel({Key? key}) : super(key: key);

  @override
  State<PerformanceDebugPanel> createState() => _PerformanceDebugPanelState();
}

class _PerformanceDebugPanelState extends State<PerformanceDebugPanel> {
  bool _isExpanded = false;
  Map<String, dynamic> _stats = {};

  @override
  void initState() {
    super.initState();
    _updateStats();
    // Update stats every 2 seconds
    Future.delayed(const Duration(seconds: 2), _updateStats);
  }

  void _updateStats() {
    if (mounted) {
      setState(() {
        _stats = AdvancedPerformanceMonitor.getPerformanceStats();
      });
      // Schedule next update
      Future.delayed(const Duration(seconds: 2), _updateStats);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Positioned(
      top: 100,
      right: 10,
      child: Material(
        elevation: 8,
        borderRadius: BorderRadius.circular(12),
        color: Colors.black.withValues(alpha: 0.8),
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          width: _isExpanded ? 280 : 60,
          height: _isExpanded ? 200 : 60,
          padding: const EdgeInsets.all(8),
          child: _isExpanded ? _buildExpandedPanel() : _buildCollapsedPanel(),
        ),
      ),
    );
  }

  Widget _buildCollapsedPanel() {
    final fps = _stats['fps']?['current'] ?? 0;
    final grade = AdvancedPerformanceMonitor.getPerformanceGrade();
    
    return GestureDetector(
      onTap: () => setState(() => _isExpanded = true),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            '${fps.toStringAsFixed(0)}',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            grade,
            style: TextStyle(
              color: _getGradeColor(grade),
              fontSize: 10,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildExpandedPanel() {
    final fps = _stats['fps'] ?? {};
    final frameTime = _stats['frameTime'] ?? {};
    final grade = AdvancedPerformanceMonitor.getPerformanceGrade();
    final isHealthy = AdvancedPerformanceMonitor.isPerformanceHealthy();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header with close button
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Performance Monitor',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
            ),
            GestureDetector(
              onTap: () => setState(() => _isExpanded = false),
              child: const Icon(Icons.close, color: Colors.white, size: 16),
            ),
          ],
        ),
        const SizedBox(height: 8),
        
        // Performance metrics
        _buildMetricRow('FPS', '${fps['current']?.toStringAsFixed(1) ?? '0'}'),
        _buildMetricRow('Avg FPS', '${fps['average']?.toStringAsFixed(1) ?? '0'}'),
        _buildMetricRow('Frame Time', '${frameTime['average']?.toStringAsFixed(1) ?? '0'}ms'),
        _buildMetricRow('Grade', grade, color: _getGradeColor(grade)),
        _buildMetricRow('Health', isHealthy ? 'Good' : 'Poor', 
                       color: isHealthy ? Colors.green : Colors.red),
        
        const SizedBox(height: 8),
        
        // Action buttons
        Row(
          children: [
            _buildActionButton('Memory', _showMemoryStats),
            const SizedBox(width: 8),
            _buildActionButton('Clear', _clearCaches),
          ],
        ),
      ],
    );
  }

  Widget _buildMetricRow(String label, String value, {Color? color}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(color: Colors.white70, fontSize: 12),
          ),
          Text(
            value,
            style: TextStyle(
              color: color ?? Colors.white,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton(String label, VoidCallback onTap) {
    return Expanded(
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 4),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(4),
          ),
          child: Text(
            label,
            textAlign: TextAlign.center,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 10,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ),
    );
  }

  Color _getGradeColor(String grade) {
    switch (grade) {
      case 'Excellent':
        return Colors.green;
      case 'Good':
        return Colors.blue;
      case 'Fair':
        return Colors.orange;
      case 'Poor':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  void _showMemoryStats() async {
    final memoryStats = await MemoryManager.getMemoryStats();
    
    Get.dialog(
      AlertDialog(
        title: const Text('Memory Statistics'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Available: ${memoryStats['available_memory_mb']}MB'),
            Text('Total: ${memoryStats['total_memory_mb']}MB'),
            Text('Image Cache: ${memoryStats['image_cache_count']} items'),
            Text('Cache Size: ${memoryStats['image_cache_size_mb']?.toStringAsFixed(1)}MB'),
            Text('Warnings: ${memoryStats['memory_warnings']}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _clearCaches() async {
    await MemoryManager.performLightCleanup();
    Get.snackbar(
      'Performance',
      'Caches cleared successfully',
      snackPosition: SnackPosition.BOTTOM,
      duration: const Duration(seconds: 2),
    );
  }
}

/// Extension to easily add performance monitoring to any screen
extension PerformanceMonitoring on Widget {
  Widget withPerformanceMonitor() {
    return Stack(
      children: [
        this,
        const PerformanceDebugPanel(),
      ],
    );
  }
}
