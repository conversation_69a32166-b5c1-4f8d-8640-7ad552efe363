import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import '../utils/enhanced_cache_manager.dart';
import '../widgets/ultra_fast_image.dart';

/// Advanced memory management system for premium performance
class MemoryManager {
  static MemoryManager? _instance;
  static Timer? _memoryMonitorTimer;
  static Timer? _cleanupTimer;
  static int _memoryWarningCount = 0;
  static const int _maxMemoryWarnings = 3;
  
  // Memory thresholds (in MB)
  static const int _lowMemoryThreshold = 100;
  static const int _criticalMemoryThreshold = 50;
  
  factory MemoryManager() {
    _instance ??= MemoryManager._internal();
    return _instance!;
  }
  
  MemoryManager._internal() {
    _startMemoryMonitoring();
    _startPeriodicCleanup();
  }
  
  /// Start monitoring memory usage
  static void _startMemoryMonitoring() {
    if (!kDebugMode) return;
    
    _memoryMonitorTimer = Timer.periodic(
      const Duration(seconds: 30),
      (_) => _checkMemoryUsage(),
    );
  }
  
  /// Start periodic cleanup
  static void _startPeriodicCleanup() {
    _cleanupTimer = Timer.periodic(
      const Duration(minutes: 5),
      (_) => performLightCleanup(),
    );
  }
  
  /// Check current memory usage
  static Future<void> _checkMemoryUsage() async {
    try {
      final memoryInfo = await _getMemoryInfo();
      final availableMemory = memoryInfo['available'] ?? 0;
      
      if (availableMemory < _criticalMemoryThreshold) {
        debugPrint('🚨 Critical memory warning: ${availableMemory}MB available');
        await performAggressiveCleanup();
        _memoryWarningCount++;
        
        if (_memoryWarningCount >= _maxMemoryWarnings) {
          debugPrint('⚠️ Multiple memory warnings - performing deep cleanup');
          await performDeepCleanup();
          _memoryWarningCount = 0;
        }
      } else if (availableMemory < _lowMemoryThreshold) {
        debugPrint('⚠️ Low memory warning: ${availableMemory}MB available');
        await performLightCleanup();
      }
    } catch (e) {
      debugPrint('Error checking memory usage: $e');
    }
  }
  
  /// Get memory information (platform-specific)
  static Future<Map<String, int>> _getMemoryInfo() async {
    try {
      if (Platform.isAndroid) {
        return await _getAndroidMemoryInfo();
      } else if (Platform.isIOS) {
        return await _getIOSMemoryInfo();
      }
    } catch (e) {
      debugPrint('Error getting memory info: $e');
    }
    
    return {'available': 1000, 'total': 2000}; // Default values
  }
  
  /// Get Android memory information
  static Future<Map<String, int>> _getAndroidMemoryInfo() async {
    try {
      const platform = MethodChannel('memory_info');
      final result = await platform.invokeMethod('getMemoryInfo');
      return Map<String, int>.from(result);
    } catch (e) {
      return {'available': 1000, 'total': 2000};
    }
  }
  
  /// Get iOS memory information
  static Future<Map<String, int>> _getIOSMemoryInfo() async {
    try {
      const platform = MethodChannel('memory_info');
      final result = await platform.invokeMethod('getMemoryInfo');
      return Map<String, int>.from(result);
    } catch (e) {
      return {'available': 1000, 'total': 2000};
    }
  }
  
  /// Perform light cleanup
  static Future<void> performLightCleanup() async {
    debugPrint('🧹 Performing light memory cleanup...');
    
    try {
      // Clear Flutter image cache
      PaintingBinding.instance.imageCache.clear();
      
      // Clear preload tracking
      UltraFastImagePreloader.clearPreloadTracking();
      
      // Force garbage collection
      await _forceGarbageCollection();
      
      debugPrint('✅ Light cleanup completed');
    } catch (e) {
      debugPrint('Error during light cleanup: $e');
    }
  }
  
  /// Perform aggressive cleanup
  static Future<void> performAggressiveCleanup() async {
    debugPrint('🚨 Performing aggressive memory cleanup...');
    
    try {
      // Clear all image caches
      PaintingBinding.instance.imageCache.clear();
      PaintingBinding.instance.imageCache.clearLiveImages();
      
      // Clear enhanced cache manager
      await EnhancedCacheManager.clearCache();
      
      // Clear preload tracking
      UltraFastImagePreloader.clearPreloadTracking();
      
      // Clear GetX caches
      _clearGetXCaches();
      
      // Force multiple garbage collections
      for (int i = 0; i < 3; i++) {
        await _forceGarbageCollection();
        await Future.delayed(const Duration(milliseconds: 100));
      }
      
      debugPrint('✅ Aggressive cleanup completed');
    } catch (e) {
      debugPrint('Error during aggressive cleanup: $e');
    }
  }
  
  /// Perform deep cleanup (last resort)
  static Future<void> performDeepCleanup() async {
    debugPrint('🔥 Performing deep memory cleanup...');
    
    try {
      // All previous cleanup steps
      await performAggressiveCleanup();
      
      // Clear all possible caches
      await _clearAllCaches();
      
      // Restart controllers if necessary
      await _restartCriticalControllers();
      
      debugPrint('✅ Deep cleanup completed');
    } catch (e) {
      debugPrint('Error during deep cleanup: $e');
    }
  }
  
  /// Clear GetX caches
  static void _clearGetXCaches() {
    try {
      // Clear GetX internal caches
      Get.clearRouteTree();
      
      // Reset controllers if they have large caches
      final controllers = Get.findAll();
      for (final controller in controllers) {
        if (controller is GetxController) {
          // Call refresh to clear internal caches
          controller.refresh();
        }
      }
    } catch (e) {
      debugPrint('Error clearing GetX caches: $e');
    }
  }
  
  /// Clear all possible caches
  static Future<void> _clearAllCaches() async {
    try {
      // Clear system caches
      await SystemChannels.platform.invokeMethod('SystemNavigator.pop');
      
      // Clear all image caches
      PaintingBinding.instance.imageCache.clear();
      PaintingBinding.instance.imageCache.clearLiveImages();
      
      // Clear enhanced cache
      await EnhancedCacheManager.clearCache();
      
    } catch (e) {
      debugPrint('Error clearing all caches: $e');
    }
  }
  
  /// Restart critical controllers
  static Future<void> _restartCriticalControllers() async {
    try {
      // This is a last resort - only restart if absolutely necessary
      debugPrint('🔄 Restarting critical controllers...');
      
      // Note: This should be implemented carefully to avoid breaking app state
      // For now, just refresh existing controllers
      final controllers = Get.findAll();
      for (final controller in controllers) {
        if (controller is GetxController) {
          controller.refresh();
        }
      }
    } catch (e) {
      debugPrint('Error restarting controllers: $e');
    }
  }
  
  /// Force garbage collection
  static Future<void> _forceGarbageCollection() async {
    // Force garbage collection (platform-specific)
    if (Platform.isAndroid || Platform.isIOS) {
      try {
        const platform = MethodChannel('memory_manager');
        await platform.invokeMethod('forceGC');
      } catch (e) {
        // Fallback: just wait for natural GC
        await Future.delayed(const Duration(milliseconds: 100));
      }
    }
  }
  
  /// Optimize widget tree for memory
  static Widget optimizeWidget(Widget child) {
    return RepaintBoundary(
      child: AutomaticKeepAliveClientMixin.wantKeepAlive
          ? child
          : _MemoryOptimizedWrapper(child: child),
    );
  }
  
  /// Get memory usage statistics
  static Future<Map<String, dynamic>> getMemoryStats() async {
    try {
      final memoryInfo = await _getMemoryInfo();
      final imageCache = PaintingBinding.instance.imageCache;
      
      return {
        'available_memory_mb': memoryInfo['available'],
        'total_memory_mb': memoryInfo['total'],
        'image_cache_count': imageCache.currentSize,
        'image_cache_size_mb': imageCache.currentSizeBytes / (1024 * 1024),
        'memory_warnings': _memoryWarningCount,
      };
    } catch (e) {
      return {'error': e.toString()};
    }
  }
  
  /// Dispose memory manager
  static void dispose() {
    _memoryMonitorTimer?.cancel();
    _cleanupTimer?.cancel();
    _memoryWarningCount = 0;
  }
}

/// Memory-optimized wrapper widget
class _MemoryOptimizedWrapper extends StatefulWidget {
  final Widget child;
  
  const _MemoryOptimizedWrapper({required this.child});
  
  @override
  State<_MemoryOptimizedWrapper> createState() => _MemoryOptimizedWrapperState();
}

class _MemoryOptimizedWrapperState extends State<_MemoryOptimizedWrapper>
    with AutomaticKeepAliveClientMixin {
  
  @override
  bool get wantKeepAlive => false; // Don't keep alive by default
  
  @override
  Widget build(BuildContext context) {
    super.build(context);
    return widget.child;
  }
  
  @override
  void dispose() {
    // Perform cleanup when widget is disposed
    super.dispose();
  }
}

/// Memory-aware image widget
class MemoryAwareImage extends StatefulWidget {
  final String imageUrl;
  final double? width;
  final double? height;
  final BoxFit fit;
  
  const MemoryAwareImage({
    Key? key,
    required this.imageUrl,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
  }) : super(key: key);
  
  @override
  State<MemoryAwareImage> createState() => _MemoryAwareImageState();
}

class _MemoryAwareImageState extends State<MemoryAwareImage> {
  bool _shouldLoad = true;
  
  @override
  void initState() {
    super.initState();
    _checkMemoryBeforeLoading();
  }
  
  Future<void> _checkMemoryBeforeLoading() async {
    final stats = await MemoryManager.getMemoryStats();
    final availableMemory = stats['available_memory_mb'] ?? 1000;
    
    if (availableMemory < 100) {
      setState(() {
        _shouldLoad = false;
      });
    }
  }
  
  @override
  Widget build(BuildContext context) {
    if (!_shouldLoad) {
      return Container(
        width: widget.width,
        height: widget.height,
        color: Colors.grey[200],
        child: const Icon(Icons.image_not_supported),
      );
    }
    
    return UltraFastImage(
      imageUrl: widget.imageUrl,
      width: widget.width,
      height: widget.height,
      fit: widget.fit,
    );
  }
}
