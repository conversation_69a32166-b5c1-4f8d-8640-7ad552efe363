// import 'dart:io';
// import 'package:flutter_image_compress/flutter_image_compress.dart';

// void main() async {
//   print('Starting image optimization...');
  
//   final assetsDir = Directory('assets/images');
//   final files = assetsDir.listSync();
  
//   for (var file in files) {
//     if (file is File && file.path.endsWith('.png')) {
//       print('Optimizing: ${file.path}');
      
//       final result = await FlutterImageCompress.compressWithFile(
//         file.absolute.path,
//         minWidth: 1920,
//         minHeight: 1080,
//         quality: 85,
//         format: CompressFormat.webp,
//       );
      
//       if (result != null) {
//         final webpPath = file.path.replaceAll('.png', '.webp');
//         File(webpPath).writeAsBytesSync(result);
//         print('Created WebP: $webpPath');
//       }
//     }
//   }
  
//   print('Image optimization complete!');
// }