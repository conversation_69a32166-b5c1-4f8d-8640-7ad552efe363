import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import '../controllers/auth_controller.dart';
import '../controllers/category_controller.dart';
import '../controllers/heart_controller.dart';
import '../controllers/lesson_controller.dart';
import '../controllers/quiz_controller.dart';
import '../controllers/profile_controller.dart';
import '../widgets/ultra_fast_image.dart';
import '../models/lesson_model.dart';

/// Ultra-fast startup system for premium app performance
class UltraFastStartup {
  static bool _isInitialized = false;
  static final Completer<void> _initCompleter = Completer<void>();

  /// Critical path initialization - only essential services
  static Future<void> initializeCriticalPath() async {
    if (_isInitialized) return;

    final stopwatch = Stopwatch()..start();
    debugPrint('🚀 Starting ultra-fast critical path initialization...');

    try {
      // Phase 1: Essential controllers only (parallel)
      await Future.wait([
        _initializeEssentialControllers(),
        _preloadCriticalAssets(),
        _optimizeSystemUI(),
      ]);

      // Phase 2: Start background initialization
      _initializeBackgroundServices();

      _isInitialized = true;
      _initCompleter.complete();

      stopwatch.stop();
      debugPrint(
          '✅ Critical path initialized in ${stopwatch.elapsedMilliseconds}ms');
    } catch (e) {
      debugPrint('❌ Critical path initialization failed: $e');
      _initCompleter.completeError(e);
      rethrow;
    }
  }

  /// Wait for critical path to complete
  static Future<void> waitForCriticalPath() async {
    if (_isInitialized) return;
    return _initCompleter.future;
  }

  /// Initialize only essential controllers for first screen
  static Future<void> _initializeEssentialControllers() async {
    debugPrint('📱 Initializing essential controllers...');

    // Only initialize controllers needed for splash/auth
    Get.put(AuthController(), permanent: true);
    Get.put(HeartController(), permanent: true);

    debugPrint('✅ Essential controllers initialized');
  }

  /// Preload critical assets that prevent layout shifts
  static Future<void> _preloadCriticalAssets() async {
    debugPrint('🎨 Preloading critical assets...');

    try {
      // Preload splash screen assets
      await Future.wait([
        rootBundle.load('assets/images/splash.png'),
        // Add other critical assets here
      ]);

      debugPrint('✅ Critical assets preloaded');
    } catch (e) {
      debugPrint('⚠️ Some critical assets failed to preload: $e');
    }
  }

  /// Optimize system UI for performance
  static Future<void> _optimizeSystemUI() async {
    debugPrint('⚡ Optimizing system UI...');

    // Set optimal system UI overlay style
    SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarBrightness: Brightness.dark,
      statusBarIconBrightness: Brightness.dark,
      systemNavigationBarColor: Colors.white,
      systemNavigationBarIconBrightness: Brightness.dark,
    ));

    // Optimize for performance
    SystemChrome.setEnabledSystemUIMode(
      SystemUiMode.edgeToEdge,
      overlays: [SystemUiOverlay.top],
    );

    debugPrint('✅ System UI optimized');
  }

  /// Initialize non-critical services in background
  static void _initializeBackgroundServices() {
    debugPrint('🔄 Starting background services initialization...');

    // Use microtask to not block the UI
    scheduleMicrotask(() async {
      try {
        await _initializeSecondaryControllers();
        await _startContentPreloading();
        debugPrint('✅ Background services initialized');
      } catch (e) {
        debugPrint('⚠️ Background services initialization failed: $e');
      }
    });
  }

  /// Initialize secondary controllers after critical path
  static Future<void> _initializeSecondaryControllers() async {
    debugPrint('📱 Initializing secondary controllers...');

    // Initialize remaining controllers
    Get.put(CategoryController(), permanent: true);
    Get.put(LessonController(), permanent: true);
    Get.put(QuizController(), permanent: true);
    Get.put(ProfileController(), permanent: true);

    debugPrint('✅ Secondary controllers initialized');
  }

  /// Start intelligent content preloading
  static Future<void> _startContentPreloading() async {
    debugPrint('🚀 Starting intelligent content preloading...');

    try {
      // Get controllers
      final categoryController = Get.find<CategoryController>();
      final lessonController = Get.find<LessonController>();
      final quizController = Get.find<QuizController>();

      // Start preloading in parallel
      await Future.wait([
        _preloadCategoryImages(categoryController),
        _preloadPriorityContent(lessonController, quizController),
      ]);

      debugPrint('✅ Content preloading completed');
    } catch (e) {
      debugPrint('⚠️ Content preloading failed: $e');
    }
  }

  /// Preload category images for instant display
  static Future<void> _preloadCategoryImages(
      CategoryController categoryController) async {
    try {
      // Wait for categories to load
      await categoryController.fetchAllCategories();

      // Extract image URLs
      final imageUrls = categoryController.allCategories
          .map((category) => category.topicPhotoLink ?? '')
          .where((url) => url.isNotEmpty)
          .toList();

      if (imageUrls.isNotEmpty) {
        await UltraFastImagePreloader.preloadForScreen(
          imageUrls,
          screenName: 'Categories',
          concurrency: 4,
        );
      }
    } catch (e) {
      debugPrint('⚠️ Category image preloading failed: $e');
    }
  }

  /// Preload priority content for instant access
  static Future<void> _preloadPriorityContent(
    LessonController lessonController,
    QuizController quizController,
  ) async {
    try {
      // Load content from local database first
      await Future.wait<void>([
        lessonController.loadContent(),
        quizController.loadQuizzes(),
        quizController.loadShuffleQuizzes(),
      ]);

      // Preload first 10 items' images for instant display
      final priorityImageUrls = <String>[];

      // Add lesson images
      final priorityLessons = lessonController.allLessons.take(5);
      for (final lesson in priorityLessons) {
        if (lesson.imageLink?.isNotEmpty == true) {
          priorityImageUrls.add(lesson.imageLink!);
        }
        // Add first page image
        final firstPage = lesson.pages?.firstOrNull;
        if (firstPage?.pagePhotoLink?.isNotEmpty == true) {
          priorityImageUrls.add(firstPage!.pagePhotoLink!);
        }
      }

      // Add quiz images
      final priorityQuizzes = quizController.allQuizzes.take(5);
      for (final quiz in priorityQuizzes) {
        if (quiz.quizImageLink?.isNotEmpty == true) {
          priorityImageUrls.add(quiz.quizImageLink!);
        }
      }

      // Add shuffle quiz images
      final priorityShuffleQuizzes = quizController.allShuffleQuizzes.take(3);
      for (final shuffleQuiz in priorityShuffleQuizzes) {
        final firstQuestion = shuffleQuiz.questionsList?.firstOrNull;
        if (firstQuestion?.qsImage?.isNotEmpty == true) {
          priorityImageUrls.add(firstQuestion!.qsImage!);
        }
      }

      if (priorityImageUrls.isNotEmpty) {
        await UltraFastImagePreloader.preloadForScreen(
          priorityImageUrls,
          screenName: 'Priority Content',
          concurrency: 8,
        );
      }
    } catch (e) {
      debugPrint('⚠️ Priority content preloading failed: $e');
    }
  }

  /// Optimize app for specific screen
  static Future<void> optimizeForScreen(String screenName) async {
    debugPrint('🎯 Optimizing for screen: $screenName');

    switch (screenName) {
      case 'home':
        await _optimizeForHome();
        break;
      case 'library':
        await _optimizeForLibrary();
        break;
      case 'lesson':
        await _optimizeForLesson();
        break;
      case 'quiz':
        await _optimizeForQuiz();
        break;
    }
  }

  static Future<void> _optimizeForHome() async {
    // Preload home screen specific content
    final lessonController = Get.find<LessonController>();
    final homeItems = lessonController.homeDisplayedItems;

    final imageUrls = <String>[];
    for (final item in homeItems) {
      if (item is LessonModel && item.imageLink?.isNotEmpty == true) {
        imageUrls.add(item.imageLink!);
      }
      // Add more item types as needed
    }

    if (imageUrls.isNotEmpty) {
      await UltraFastImagePreloader.preloadForScreen(
        imageUrls,
        screenName: 'Home',
        concurrency: 6,
      );
    }
  }

  static Future<void> _optimizeForLibrary() async {
    // Similar optimization for library screen
    debugPrint('📚 Optimizing library screen...');
  }

  static Future<void> _optimizeForLesson() async {
    // Optimize for lesson viewing
    debugPrint('📖 Optimizing lesson screen...');
  }

  static Future<void> _optimizeForQuiz() async {
    // Optimize for quiz taking
    debugPrint('🧠 Optimizing quiz screen...');
  }
}
