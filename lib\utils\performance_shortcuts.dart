import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'advanced_performance_monitor.dart';
import 'memory_manager.dart';

/// Performance monitoring shortcuts and quick checks
class PerformanceShortcuts {
  static bool _isInitialized = false;

  /// Initialize performance shortcuts
  static void initialize() {
    if (_isInitialized || !kDebugMode) return;

    // Register keyboard shortcuts for performance monitoring
    _registerShortcuts();
    _isInitialized = true;

    debugPrint('🎯 Performance shortcuts initialized:');
    debugPrint('   Ctrl+P: Performance stats');
    debugPrint('   Ctrl+M: Memory stats');
    debugPrint('   Ctrl+C: Clear caches');
    debugPrint('   Ctrl+F: FPS info');
  }

  /// Register keyboard shortcuts
  static void _registerShortcuts() {
    // Note: This is a simplified version for demonstration
    // In a real app, you'd integrate with Flutter's shortcuts system
  }

  /// Quick performance check - call this anytime
  static void quickCheck() {
    if (!kDebugMode) return;

    final stats = AdvancedPerformanceMonitor.getPerformanceStats();
    final grade = AdvancedPerformanceMonitor.getPerformanceGrade();
    final isHealthy = AdvancedPerformanceMonitor.isPerformanceHealthy();

    debugPrint('🚀 QUICK PERFORMANCE CHECK:');
    debugPrint('   Grade: $grade');
    debugPrint('   Health: ${isHealthy ? "✅ Good" : "❌ Poor"}');

    if (stats['fps'] != null) {
      final fps = stats['fps'];
      debugPrint(
          '   FPS: Current=${fps['current']?.toStringAsFixed(1)}, Avg=${fps['average']?.toStringAsFixed(1)}');
    }

    if (stats['frameTime'] != null) {
      final frameTime = stats['frameTime'];
      debugPrint(
          '   Frame Time: Avg=${frameTime['average']?.toStringAsFixed(1)}ms');
    }

    // Show warning if performance is poor
    if (!isHealthy) {
      debugPrint('⚠️ PERFORMANCE WARNING: App may be running slowly!');
      _suggestOptimizations();
    }
  }

  /// Show detailed performance stats
  static void showDetailedStats() {
    if (!kDebugMode) return;

    final stats = AdvancedPerformanceMonitor.getPerformanceStats();

    debugPrint('📊 DETAILED PERFORMANCE STATS:');
    debugPrint('================================');

    // FPS Stats
    if (stats['fps'] != null) {
      final fps = stats['fps'];
      debugPrint('FPS:');
      debugPrint('  Current: ${fps['current']?.toStringAsFixed(1)}');
      debugPrint('  Average: ${fps['average']?.toStringAsFixed(1)}');
      debugPrint('  Minimum: ${fps['minimum']?.toStringAsFixed(1)}');
      debugPrint('  Maximum: ${fps['maximum']?.toStringAsFixed(1)}');
    }

    // Frame Time Stats
    if (stats['frameTime'] != null) {
      final frameTime = stats['frameTime'];
      debugPrint('Frame Time:');
      debugPrint('  Average: ${frameTime['average']?.toStringAsFixed(1)}ms');
      debugPrint('  Maximum: ${frameTime['maximum']}ms');
    }

    // Custom Metrics
    if (stats['customMetrics'] != null) {
      final metrics = stats['customMetrics'] as Map<String, dynamic>;
      if (metrics.isNotEmpty) {
        debugPrint('Custom Metrics:');
        metrics.forEach((key, value) {
          if (value is Map) {
            debugPrint(
                '  $key: avg=${value['average']?.toStringAsFixed(1)}, count=${value['count']}');
          }
        });
      }
    }

    debugPrint('================================');
  }

  /// Show memory statistics
  static Future<void> showMemoryStats() async {
    if (!kDebugMode) return;

    try {
      final memoryStats = await MemoryManager.getMemoryStats();

      debugPrint('🧠 MEMORY STATISTICS:');
      debugPrint('====================');
      debugPrint('Available Memory: ${memoryStats['available_memory_mb']}MB');
      debugPrint('Total Memory: ${memoryStats['total_memory_mb']}MB');
      debugPrint('Image Cache Count: ${memoryStats['image_cache_count']}');
      debugPrint(
          'Image Cache Size: ${memoryStats['image_cache_size_mb']?.toStringAsFixed(1)}MB');
      debugPrint('Memory Warnings: ${memoryStats['memory_warnings']}');
      debugPrint('====================');

      // Memory health check
      final availableMemory = memoryStats['available_memory_mb'] ?? 1000;
      if (availableMemory < 100) {
        debugPrint(
            '⚠️ LOW MEMORY WARNING: Only ${availableMemory}MB available!');
      } else if (availableMemory < 200) {
        debugPrint('⚠️ Memory getting low: ${availableMemory}MB available');
      } else {
        debugPrint('✅ Memory usage is healthy');
      }
    } catch (e) {
      debugPrint('❌ Error getting memory stats: $e');
    }
  }

  /// Clear caches and show result
  static Future<void> clearCachesAndReport() async {
    if (!kDebugMode) return;

    debugPrint('🧹 Clearing caches...');

    try {
      await MemoryManager.performLightCleanup();
      debugPrint('✅ Caches cleared successfully');

      // Show memory stats after cleanup
      await Future.delayed(const Duration(milliseconds: 500));
      await showMemoryStats();
    } catch (e) {
      debugPrint('❌ Error clearing caches: $e');
    }
  }

  /// Check if app is performing well
  static bool isAppPerformingWell() {
    final isHealthy = AdvancedPerformanceMonitor.isPerformanceHealthy();
    final grade = AdvancedPerformanceMonitor.getPerformanceGrade();

    return isHealthy && (grade == 'Excellent' || grade == 'Good');
  }

  /// Get performance summary for quick overview
  static String getPerformanceSummary() {
    final stats = AdvancedPerformanceMonitor.getPerformanceStats();
    final grade = AdvancedPerformanceMonitor.getPerformanceGrade();
    final isHealthy = AdvancedPerformanceMonitor.isPerformanceHealthy();

    final fps = stats['fps']?['current']?.toStringAsFixed(1) ?? 'N/A';
    final health = isHealthy ? 'Good' : 'Poor';

    return 'FPS: $fps | Grade: $grade | Health: $health';
  }

  /// Suggest optimizations based on current performance
  static void _suggestOptimizations() {
    debugPrint('💡 OPTIMIZATION SUGGESTIONS:');
    debugPrint('   1. Check for heavy operations on main thread');
    debugPrint('   2. Reduce image sizes or use lower quality');
    debugPrint(
        '   3. Clear caches: PerformanceShortcuts.clearCachesAndReport()');
    debugPrint(
        '   4. Check memory usage: PerformanceShortcuts.showMemoryStats()');
    debugPrint('   5. Restart app if performance doesn\'t improve');
  }

  /// Monitor performance continuously (for debugging)
  static void startContinuousMonitoring(
      {Duration interval = const Duration(seconds: 5)}) {
    if (!kDebugMode) return;

    debugPrint('🔄 Starting continuous performance monitoring...');

    Timer.periodic(interval, (timer) {
      final summary = getPerformanceSummary();
      debugPrint('📊 Performance: $summary');

      if (!isAppPerformingWell()) {
        debugPrint('⚠️ Performance issue detected!');
        quickCheck();
      }
    });
  }
}

/// Extension for easy performance monitoring in widgets
extension PerformanceMonitoringWidget on Widget {
  /// Add performance monitoring to any widget
  Widget withPerformanceCheck(String screenName) {
    return Builder(
      builder: (context) {
        // Record screen navigation
        AdvancedPerformanceMonitor.recordMetric('screen_$screenName', 1);

        return this;
      },
    );
  }
}

/// Global performance check functions (easy to call from anywhere)
void checkPerformance() => PerformanceShortcuts.quickCheck();
void showPerformanceStats() => PerformanceShortcuts.showDetailedStats();
Future<void> checkMemory() => PerformanceShortcuts.showMemoryStats();
Future<void> clearCaches() => PerformanceShortcuts.clearCachesAndReport();
String getPerformanceSummary() => PerformanceShortcuts.getPerformanceSummary();
